import {
  http
} from '@/common/request/index.js'; // 局部引入

const api = {
  getAgentList: '/wechat/sign/getAgentList',
  getAgentEmployeeList: '/wechat/sign/getAgentEmployeeList',
  saveAgentEmployee: '/wechat/sign/saveAgentEmployee',
  getCoachedUserWechatVo: '/wechat/sign/getCoachedUserWechatVo'
}

// 获取代理商列表
export const getAgentList = (params) => {
  // 模拟API调用
  return new Promise((resolve) => {
    setTimeout(() => {
      const { type, keyword, provinceCode, cityCode, pageNum = 1, pageSize = 10 } = params

      let filteredList = [...mockAgentList]

      // 根据类型过滤
      if (type === 'reserve') {
        // 储备代理商逻辑
      } else if (type === 'formal') {
        // 正式代理商逻辑
      }

      // 根据关键词过滤
      if (keyword) {
        filteredList = filteredList.filter(item =>
          item.companyName.includes(keyword) ||
          item.phone.includes(keyword)
        )
      }

      // 根据省市过滤
      if (provinceCode) {
        filteredList = filteredList.filter(item => item.provinceCode === provinceCode)
      }
      if (cityCode) {
        filteredList = filteredList.filter(item => item.cityCode === cityCode)
      }

      // 分页
      const start = (pageNum - 1) * pageSize
      const end = start + pageSize
      const list = filteredList.slice(start, end)

      resolve({
        code: 200,
        data: {
          list,
          total: filteredList.length
        },
        message: 'success'
      })
    }, 500)
  })
}

// 新增代理商
export const addAgent = (data) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      // 模拟新增成功
      resolve({
        code: 200,
        data: { id: Date.now().toString() },
        message: '新增成功'
      })
    }, 1000)
  })
}

// 编辑代理商
export const editAgent = (data) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      // 模拟编辑成功
      resolve({
        code: 200,
        data: null,
        message: '编辑成功'
      })
    }, 1000)
  })
}

// 删除代理商
export const deleteAgent = (id) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      // 模拟删除成功
      resolve({
        code: 200,
        data: null,
        message: '删除成功'
      })
    }, 500)
  })
}

// 获取代理商详情
export const getAgentDetail = (id) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      // 根据ID返回对应的详情
      const agent = mockAgentList.find(item => item.id === id)
      if (agent) {
        resolve({
          code: 200,
          data: agent,
          message: 'success'
        })
      } else {
        resolve({
          code: 404,
          data: null,
          message: '代理商不存在'
        })
      }
    }, 500)
  })
}

// 获取意向产品列表
export const getProductList = (params) => {
  return http.get('/product/list', { params })
}

// 获取意向终端列表
export const getTerminalList = (params) => {
  return http.get('/terminal/list', { params })
}

// 获取团队人员列表
export const getTeamList = (params) => {
  return http.get('/team/list', { params })
}
